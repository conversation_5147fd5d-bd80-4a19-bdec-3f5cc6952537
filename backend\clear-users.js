const mongoose = require('mongoose');
require('dotenv').config();
const User = require('./models/User');
const Form = require('./models/FormModel');

async function clearUsers() {
  try {
    await mongoose.connect(process.env.MONGO_URI, {
      useNewUrlParser: true,
      useUnifiedTopology: true
    });
    
    console.log('Connected to MongoDB');
    
    // Count existing users
    const userCount = await User.countDocuments();
    const formCount = await Form.countDocuments();
    
    console.log(`Found ${userCount} users in User collection`);
    console.log(`Found ${formCount} users in Form collection`);
    
    // Clear all users (uncomment the lines below to actually delete)
    // await User.deleteMany({});
    // await Form.deleteMany({});
    
    console.log('To actually clear users, uncomment the delete lines in this script');
    console.log('Users NOT deleted - this was just a preview');
    
    await mongoose.disconnect();
    console.log('Disconnected from MongoDB');
  } catch (error) {
    console.error('Error:', error);
  }
}

clearUsers();
