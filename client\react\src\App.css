* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

#root {
  width: 100%;
  height: 100vh;
}

.app-container {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
}

/* Header Styles */
.header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

.logo-container {
  display: flex;
  align-items: center;
}

.logo-text {
  font-size: 1.8rem;
  font-weight: bold;
  letter-spacing: 1px;
}

.vital {
  color: #e63946 !important; /* Contact page red - elegant and professional */
  text-shadow: 0 1px 2px rgba(230, 57, 70, 0.2);
}

.drop {
  color: #1d3557 !important; /* Contact page dark blue - sophisticated */
  text-shadow: 0 1px 2px rgba(29, 53, 87, 0.1);
}

.navigation ul {
  display: flex;
  list-style: none;
  gap: 2rem;
}

.navigation a {
  text-decoration: none;
  color: #1d3557;
  font-weight: 500;
  font-size: 1.1rem;
  transition: color 0.3s ease;
}

.navigation a:hover, .navigation a.active {
  color: #e63946;
}

/* Hero Section Styles */
.hero-section {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  padding: 0 2rem;
  background-image: url('./background.png');
  background-size: cover;
  background-position: center;
  position: relative;
}

.hero-section::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-color: rgba(255, 255, 255, 0.2);
}

.hero-content {
  position: relative;
  max-width: 800px;
  padding: 2rem;
  border-radius: 8px;
  margin-right: 2%;
  margin-left: calc(5% + 10.5cm);
  z-index: 1;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
}

.tagline {
  font-size: 2rem;
  color: #1d3557;
  margin-bottom: 1rem;
  line-height: 1.5;
  align-self: flex-start;
  margin-left: calc(-55px + 10.5cm);
  max-width: 100%;
  text-align: left;
}



.highlight {
  color: #e63946;
  font-weight: bold;
}

.cta-row {
  display: flex;
  flex-direction: row;
  align-items: center;
  gap: 2rem;
  margin-bottom: 0;
}

.sub-tagline {
  font-size: 1.5rem;
  color: #1d3557;
  margin-bottom: 0;
  align-self: flex-start;
  margin-left: calc(15px + 10.5cm);
}

.donate-button {
  background-color: #e63946;
  color: white;
  border: none;
  padding: 0.8rem 2rem;
  font-size: 1.2rem;
  font-weight: bold;
  border-radius: 50px;
  cursor: pointer;
  transition: background-color 0.3s ease;
  box-shadow: 0 4px 8px rgba(0, 0, 0, 0.2);
  align-self: center;
  margin-right: 0;
  margin-left: calc(15px + 10.5cm);
  margin-top: 2rem;
}

.vital-text {
  color: #e63946;
  font-weight: bold;
}

.drop-text {
  color: #000000;
  font-weight: bold;
}

.donate-button:hover {
  background-color: #c1121f;
}

/* Responsive Styles */
@media (max-width: 768px) {
  .header {
    flex-direction: column;
    padding: 1rem;
  }
  
  .navigation ul {
    margin-top: 1rem;
    gap: 1rem;
  }
  
  .hero-section {
    justify-content: center;
    text-align: center;
  }
  
  .hero-content {
    margin-right: 0;
    padding: 1.5rem;
  }
  
  .tagline {
    font-size: 2rem;
  }
}

/* Donation Page Animations */
@keyframes heartbeat {
  0%, 100% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
}

@keyframes pulse {
  0%, 100% {
    opacity: 0.8;
  }
  50% {
    opacity: 1;
  }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes bounceIn {
  0% {
    opacity: 0;
    transform: scale(0.3);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  70% {
    transform: scale(0.9);
  }
  100% {
    opacity: 1;
    transform: scale(1);
  }
}

/* Apply animations to donation page elements */
.donation-hero-heart {
  animation: heartbeat 2s ease-in-out infinite;
}

.donation-hero-bg {
  animation: pulse 4s ease-in-out infinite;
}

.donation-stats-card {
  animation: fadeInUp 0.8s ease-out;
}

.donation-form-section {
  animation: slideInLeft 1s ease-out;
}

.donation-success-section {
  animation: bounceIn 1.2s ease-out;
}
