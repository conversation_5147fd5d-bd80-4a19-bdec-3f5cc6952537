/* Navbar Container */
.navbar {
  width: 100%;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 1rem 2rem;
  background-color: white;
  box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
}

/* Logo Styling */
.logo-container {
  display: flex;
  align-items: center;
}

.logo-text {
  font-size: 1.8rem;
  font-weight: bold;
  letter-spacing: 1px;
  margin: 0;
  display: flex;
  align-items: center;
}

.vital {
  color: #dc2626; /* More vibrant red */
  font-weight: bold;
}

.drop {
  color: #000000; /* Pure black */
  font-weight: bold;
}

/* Navigation */
.navigation {
  display: flex;
}

.nav-links {
  display: flex;
  list-style: none;
  gap: 2rem;
  padding: 0;
  margin: 0;
}

.nav-links li {
  display: inline-block;
}

.nav-links a {
  text-decoration: none;
  color: #1d3557;
  font-weight: 500;
  font-size: 1.1rem;
  transition: color 0.3s ease;
}

.nav-links a:hover, 
.nav-links a.active {
  color: #e63946;
}

/* Responsive Design */
@media (max-width: 768px) {
  .navbar {
    flex-direction: column;
    align-items: center;
    text-align: center;
    padding: 1rem;
  }

  .nav-links {
    flex-direction: column;
    gap: 1rem;
    align-items: center;
  }
}